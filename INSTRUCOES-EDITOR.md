# Instruções para Configurar Editor (Manual)

## 🎯 Objetivo
Configurar VS Code/Cursor para **NÃO** alterar espaços em expressões como `if(! $var)`.

## 📋 Para VS Code/Cursor:

### 1. Abrir Configurações do Workspace
- Pressionar `Cmd+Shift+P` (Mac) ou `Ctrl+Shift+P` (Windows/Linux)
- Digitar: `Preferences: Open Workspace Settings (JSON)`
- Se não existir, criar arquivo `.vscode/settings.json`

### 2. Adicionar Configurações
```json
{
    "editor.formatOnSave": false,
    "editor.formatOnPaste": false,
    "editor.formatOnType": false,
    "editor.codeActionsOnSave": {
        "source.fixAll": "never",
        "source.organizeImports": "never"
    },
    "php.validate.enable": true,
    "php.suggest.basic": false,
    "php.format.enable": false,
    "php.format.standard": "none",
    "files.trimTrailingWhitespace": false,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": false,
    "[php]": {
        "editor.formatOnSave": false,
        "editor.formatOnPaste": false,
        "editor.formatOnType": false,
        "editor.defaultFormatter": null
    }
}
```

### 3. Reiniciar VS Code/Cursor
- Fechar e abrir novamente o editor
- Ou: `Cmd+Shift+P` > `Developer: Reload Window`

## ✅ Resultado
- ❌ **Formatação automática desabilitada**
- ✅ **Espaços preservados** em `if(! $var)`
- ✅ **Indentação mantida** (via .editorconfig)
- ✅ **Controle total** sobre formatação

## 🎨 Para Cursor Específico:
Se usar Cursor, também criar `.cursor/settings.json` com o mesmo conteúdo.

## 🔧 Verificar se Funcionou:
1. Digitar: `if(! $teste) {`
2. Pressionar Enter ou Ctrl+S
3. **Deve manter** o espaço entre `!` e `$teste`

## 🚨 Se Não Funcionar:
1. Verificar extensões PHP conflitantes
2. Verificar configurações globais do usuário
3. Desabilitar outras extensões de formatação PHP
