#!/bin/bash

# Script para formatação automática ao salvar
# Pode ser configurado como extensão do VS Code/Cursor

set -e

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Verificar se estamos usando <PERSON>l
if [ -f "./vendor/bin/sail" ]; then
    COMPOSER_CMD="./vendor/bin/sail composer"
    echo -e "${YELLOW}Usando <PERSON>${NC}"
else
    COMPOSER_CMD="composer"
    echo -e "${YELLOW}Usando PHP local${NC}"
fi

# Função para formatar arquivo específico
format_file() {
    local file_path="$1"
    
    # Verificar se é arquivo PHP
    if [[ "$file_path" != *.php ]]; then
        return 0
    fi
    
    echo -e "${YELLOW}Formatando: $file_path${NC}"
    
    # Verificar se PHP CS Fixer está disponível
    if $COMPOSER_CMD show | grep -q "friendsofphp/php-cs-fixer"; then
        # Formatar com PHP CS Fixer
        if [ -f "./vendor/bin/sail" ]; then
            ./vendor/bin/sail composer exec php-cs-fixer -- fix "$file_path" --config=.php-cs-fixer.php
        else
            composer exec php-cs-fixer -- fix "$file_path" --config=.php-cs-fixer.php
        fi
        echo -e "${GREEN}✓ Arquivo formatado com PHP CS Fixer${NC}"
    else
        # Formatação básica sem PHP CS Fixer
        echo -e "${YELLOW}PHP CS Fixer não encontrado. Aplicando formatação básica...${NC}"
        
        # Corrigir indentação básica (preservando espaços)
        # Usar sed para corrigir apenas indentação excessiva
        sed -i '' 's/^        /    /g' "$file_path" 2>/dev/null || true
        
        echo -e "${GREEN}✓ Formatação básica aplicada${NC}"
    fi
}

# Função principal
main() {
    if [ $# -eq 0 ]; then
        echo "Uso: $0 <arquivo.php>"
        echo "Exemplo: $0 app/Services/ExampleService.php"
        exit 1
    fi
    
    local file_path="$1"
    
    if [ ! -f "$file_path" ]; then
        echo "Erro: Arquivo não encontrado: $file_path"
        exit 1
    fi
    
    format_file "$file_path"
}

# Executar função principal
main "$@"
