#!/bin/bash

# Script para aplicar formatação PHP manualmente
# Uso: ./scripts/format-php.sh [--check] [--fix] [--install]

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Verificar se estamos usando Sail
if [ -f "./vendor/bin/sail" ]; then
    PHP_CMD="./vendor/bin/sail exec laravel.test php"
    COMPOSER_CMD="./vendor/bin/sail composer"
    echo -e "${YELLOW}Usando Laravel Sail para execução${NC}"
else
    PHP_CMD="php"
    COMPOSER_CMD="composer"
    echo -e "${YELLOW}Usando PHP local${NC}"
fi

# Verificar se PHP CS Fixer está disponível
check_php_cs_fixer() {
    if [ -f "./vendor/bin/sail" ]; then
        ./vendor/bin/sail composer show | grep -q "friendsofphp/php-cs-fixer"
        return $?
    else
        composer show | grep -q "friendsofphp/php-cs-fixer"
        return $?
    fi
}

# Função para mostrar ajuda
show_help() {
    echo "Uso: $0 [OPÇÃO]"
    echo ""
    echo "Opções:"
    echo "  --check     Verificar formatação sem alterar arquivos"
    echo "  --fix       Aplicar formatação automaticamente"
    echo "  --install   Instalar PHP CS Fixer (opcional)"
    echo "  --help      Mostrar esta ajuda"
    echo ""
    echo "Exemplo:"
    echo "  $0 --check    # Verificar formatação"
    echo "  $0 --fix      # Aplicar formatação"
    echo "  $0 --install  # Instalar ferramenta de formatação"
    echo ""
    echo "Nota: Este script usa Laravel Sail se disponível"
    echo "      PHP CS Fixer é opcional - script funciona sem ele"
}

# Função para instalar PHP CS Fixer
install_php_cs_fixer() {
    echo -e "${BLUE}Instalando PHP CS Fixer...${NC}"
    echo -e "${YELLOW}Isso adicionará dependências ao projeto.${NC}"
    echo -e "${YELLOW}Todos os membros do time precisarão rodar 'composer install'${NC}"
    echo ""
    read -p "Continuar? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Instalando...${NC}"
        $COMPOSER_CMD require --dev friendsofphp/php-cs-fixer
        echo -e "${GREEN}✓ PHP CS Fixer instalado com sucesso!${NC}"
        echo -e "${BLUE}Lembre-se de commitar composer.json e composer.lock${NC}"
    else
        echo -e "${YELLOW}Instalação cancelada.${NC}"
        exit 0
    fi
}

# Função para verificar formatação
check_format() {
    if check_php_cs_fixer; then
        echo -e "${YELLOW}Verificando formatação com PHP CS Fixer...${NC}"
        
        # Tentar executar via Sail primeiro
        if [ -f "./vendor/bin/sail" ]; then
            # Verificar se o container está rodando
            if ./vendor/bin/sail ps | grep -q "laravel.test"; then
                ./vendor/bin/sail composer exec php-cs-fixer -- fix --dry-run --diff --config=.php-cs-fixer.php
            else
                echo -e "${YELLOW}Container Sail não está rodando. Iniciando...${NC}"
                ./vendor/bin/sail up -d
                sleep 5
                ./vendor/bin/sail composer exec php-cs-fixer -- fix --dry-run --diff --config=.php-cs-fixer.php
            fi
        else
            composer exec php-cs-fixer -- fix --dry-run --diff --config=.php-cs-fixer.php
        fi
        
        echo -e "${GREEN}✓ Verificação com PHP CS Fixer concluída${NC}"
    else
        echo -e "${YELLOW}PHP CS Fixer não encontrado.${NC}"
        echo -e "${BLUE}Verificando apenas com ferramentas nativas...${NC}"
        
        # Verificações básicas sem PHP CS Fixer
        echo -e "${YELLOW}Verificando indentação básica...${NC}"
        
        # Verificar arquivos PHP com indentação incorreta (mais de 4 espaços no início)
        find app config database resources routes tests -name "*.php" -type f -exec grep -l "^        " {} \; | head -5 | while read file; do
            echo -e "${RED}⚠️  Possível indentação incorreta em: $file${NC}"
        done
        
        echo -e "${GREEN}✓ Verificação básica concluída${NC}"
        echo -e "${BLUE}Para verificação completa, instale PHP CS Fixer: $0 --install${NC}"
    fi
}

# Função para aplicar formatação
fix_format() {
    if check_php_cs_fixer; then
        echo -e "${YELLOW}Aplicando formatação com PHP CS Fixer...${NC}"
        
        # Tentar executar via Sail primeiro
        if [ -f "./vendor/bin/sail" ]; then
            # Verificar se o container está rodando
            if ./vendor/bin/sail ps | grep -q "laravel.test"; then
                ./vendor/bin/sail composer exec php-cs-fixer -- fix --config=.php-cs-fixer.php
            else
                echo -e "${YELLOW}Container Sail não está rodando. Iniciando...${NC}"
                ./vendor/bin/sail up -d
                sleep 5
                ./vendor/bin/sail composer exec php-cs-fixer -- fix --config=.php-cs-fixer.php
            fi
        else
            composer exec php-cs-fixer -- fix --config=.php-cs-fixer.php
        fi
        
        echo -e "${GREEN}✓ Formatação com PHP CS Fixer aplicada${NC}"
    else
        echo -e "${YELLOW}PHP CS Fixer não encontrado.${NC}"
        echo -e "${BLUE}Para formatação automática, instale PHP CS Fixer: $0 --install${NC}"
        echo -e "${YELLOW}Ou use as configurações do editor (.editorconfig, .vscode/settings.json)${NC}"
    fi
}

# Processar argumentos
case "${1:-}" in
    --check)
        check_format
        ;;
    --fix)
        fix_format
        ;;
    --install)
        install_php_cs_fixer
        ;;
    --help|-h)
        show_help
        ;;
    "")
        echo -e "${YELLOW}Nenhuma opção especificada. Use --help para ver as opções.${NC}"
        show_help
        exit 1
        ;;
    *)
        echo -e "${RED}Opção inválida: $1${NC}"
        show_help
        exit 1
        ;;
esac
