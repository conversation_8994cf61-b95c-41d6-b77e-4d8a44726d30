<?php

declare(strict_types=1);

namespace Tests\Feature\Exceptions\BankAccount\Transfers\Pix;

use App\Exceptions\BankAccount\Transfers\Pix\StatusPixException;
use Tests\TestCase;

class StatusPixExceptionTest extends TestCase
{
    public function test_can_create_exception_from_qitech_error_code(): void
    {
        $exception = StatusPixException::fromQiTechError('PXT000023');

        $this->assertEquals('Transferência PIX de saída não foi encontrada.', $exception->getMessage());
        $this->assertEquals(404, $exception->getStatusCode());
        $this->assertEquals('PXT000023', $exception->getErrorCode());
        $this->assertEquals('pix_status_query_error', $exception->getErrorType());
    }

    public function test_can_create_exception_from_api_response(): void
    {
        $responseData = [
            'title' => 'Outgoing PIX Transfer Not Found',
            'description' => 'Pix transfer key was not found',
            'translation' => 'Transferência PIX de saída não foi encontrada.',
            'code' => 'PXT000023',
            'extra_fields' => []
        ];

        $exception = StatusPixException::fromApiResponse($responseData, 404);

        $this->assertEquals('Transferência PIX de saída não foi encontrada.', $exception->getMessage());
        $this->assertEquals(404, $exception->getStatusCode());
        $this->assertEquals('PXT000023', $exception->getErrorCode());
        $this->assertEquals('pix_status_query_error', $exception->getErrorType());
    }

    public function test_can_create_exception_for_transfer_not_found(): void
    {
        $pixTransferKey = '8cb70dea-9fb0-4a68-9572-99a72849c8d6';
        $exception = StatusPixException::fromTransferNotFound($pixTransferKey);

        $this->assertStringContainsString($pixTransferKey, $exception->getMessage());
        $this->assertEquals(404, $exception->getStatusCode());
        $this->assertEquals('PXT000023', $exception->getErrorCode());
        $this->assertEquals('transfer_not_found', $exception->getErrorType());
    }

    public function test_can_create_exception_for_unauthorized_user(): void
    {
        $exception = StatusPixException::fromUnauthorizedUser();

        $this->assertEquals('Usuário não tem autorização para fazer essa transação.', $exception->getMessage());
        $this->assertEquals(403, $exception->getStatusCode());
        $this->assertEquals('PIT000001', $exception->getErrorCode());
        $this->assertEquals('unauthorized_user', $exception->getErrorType());
    }

    public function test_can_create_exception_for_invalid_parameters(): void
    {
        $exception = StatusPixException::fromInvalidParameters();

        $this->assertEquals('Não foram fornecidos uma pix transfer key ou end to end id.', $exception->getMessage());
        $this->assertEquals(400, $exception->getStatusCode());
        $this->assertEquals('PXT000075', $exception->getErrorCode());
        $this->assertEquals('invalid_parameters', $exception->getErrorType());
    }

    public function test_can_create_generic_exception(): void
    {
        $exception = StatusPixException::fromGenericError('Custom error message', 500);

        $this->assertEquals('Custom error message', $exception->getMessage());
        $this->assertEquals(500, $exception->getStatusCode());
        $this->assertEquals('UNKNOWN_CODE', $exception->getErrorCode());
        $this->assertEquals('generic_error', $exception->getErrorType());
    }

    public function test_can_check_if_error_code_is_known(): void
    {
        $this->assertTrue(StatusPixException::isKnownErrorCode('PXT000023'));
        $this->assertTrue(StatusPixException::isKnownErrorCode('PXT000075'));
        $this->assertFalse(StatusPixException::isKnownErrorCode('UNKNOWN_CODE'));
    }

    public function test_can_get_error_message_for_code(): void
    {
        $message = StatusPixException::getErrorMessage('PXT000023');
        $this->assertEquals('Transferência PIX de saída não foi encontrada.', $message);

        $unknownMessage = StatusPixException::getErrorMessage('UNKNOWN_CODE');
        $this->assertNull($unknownMessage);
    }

    public function test_can_get_http_status_code_for_error_code(): void
    {
        $statusCode = StatusPixException::getHttpStatusCode('PXT000023');
        $this->assertEquals(404, $statusCode);

        $defaultStatusCode = StatusPixException::getHttpStatusCode('UNKNOWN_CODE');
        $this->assertEquals(422, $defaultStatusCode);
    }

    public function test_handles_unknown_error_codes_gracefully(): void
    {
        $exception = StatusPixException::fromQiTechError('UNKNOWN_CODE');

        $this->assertEquals('Erro desconhecido na consulta de status PIX.', $exception->getMessage());
        $this->assertEquals(422, $exception->getStatusCode());
        $this->assertEquals('UNKNOWN_CODE', $exception->getErrorCode());
    }

    public function test_api_response_uses_translation_over_description(): void
    {
        $responseData = [
            'title' => 'Error Title',
            'description' => 'English description',
            'translation' => 'Descrição em português',
            'code' => 'PXT000023'
        ];

        $exception = StatusPixException::fromApiResponse($responseData, 400);

        $this->assertEquals('Descrição em português', $exception->getMessage());
    }

    public function test_api_response_falls_back_to_description_when_no_translation(): void
    {
        $responseData = [
            'title' => 'Error Title',
            'description' => 'English description',
            'code' => 'PXT000023'
        ];

        $exception = StatusPixException::fromApiResponse($responseData, 400);

        $this->assertEquals('English description', $exception->getMessage());
    }

    public function test_api_response_uses_default_message_when_no_description_or_translation(): void
    {
        $responseData = [
            'title' => 'Error Title',
            'code' => 'PXT000023'
        ];

        $exception = StatusPixException::fromApiResponse($responseData, 400);

        $this->assertEquals('Transferência PIX de saída não foi encontrada.', $exception->getMessage());
    }
}
