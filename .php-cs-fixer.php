<?php

declare(strict_types=1);

$finder = PhpCsFixer\Finder::create()
    ->in([
        __DIR__ . '/app',
        __DIR__ . '/config',
        __DIR__ . '/database',
        __DIR__ . '/resources',
        __DIR__ . '/routes',
        __DIR__ . '/tests',
    ])
    ->exclude([
        'vendor',
        'bootstrap/cache',
        'storage',
        'node_modules',
    ]);

$config = new PhpCsFixer\Config();

return $config
    ->setRules([
        // Regras básicas PSR-12
        '@PSR12' => true,
        
        // Regras de indentação e estrutura (IMPORTANTE: manter ativas)
        'indentation_type' => true, // Força indentação com espaços
        'braces' => true, // Força formatação de chaves
        'method_chaining_indentation' => true, // Indentação de métodos encadeados
        'array_indentation' => true, // Indentação de arrays
        
        // IMPORTANTE: Manter regra para remover imports não utilizados
        'no_unused_imports' => true, // Remove imports não utilizados
        
        // Desabilitar regras que causam mudanças desnecessárias
        'unary_operator_spaces' => false, // Não forçar espaços em operadores unários (!, +, -, etc)
        'spaces_inside_parentheses' => false, // Não forçar espaços dentro de parênteses
        
        // Manter espaços existentes
        'no_spaces_around_offset' => false,
        'no_spaces_inside_parenthesis' => false,
        
        // Regras de formatação que preservam estilo existente
        'array_syntax' => ['syntax' => 'short'],
        'ordered_imports' => ['sort_algorithm' => 'alpha'],
        'no_leading_import_slash' => true,
        'no_trailing_comma_in_singleline' => true,
        'no_singleline_whitespace_before_semicolons' => true,
        'no_empty_statement' => true,
        'no_extra_blank_lines' => true,
        'no_trailing_whitespace' => true,
        'no_trailing_whitespace_in_comment' => true,
        'no_whitespace_in_blank_line' => true,
        'single_blank_line_at_eof' => true,
        'no_closing_tag' => true,
        'line_ending' => true,
        'function_typehint_space' => true,
        'single_line_comment_style' => true,
        'no_empty_comment' => true,
        'no_blank_lines_after_class_opening' => true,
        'no_blank_lines_after_phpdoc' => true,
        'no_break_comment' => true,
        'no_empty_phpdoc' => true,
        'no_leading_namespace_whitespace' => true,
        'no_trailing_comma_in_list_call' => true,
        'no_trailing_comma_in_singleline_array' => true,
        'no_whitespace_before_comma_in_array' => true,
        'normalize_index_brace' => true,
        'object_operator_without_whitespace' => true,
        'phpdoc_indent' => true,
        'phpdoc_no_access' => true,
        'phpdoc_no_package' => true,
        'phpdoc_scalar' => true,
        'phpdoc_single_line_var_spacing' => true,
        'phpdoc_trim' => true,
        'phpdoc_var_without_name' => true,
        'single_line_after_imports' => true,
        'single_quote' => true,
        'standardize_not_equals' => true,
        'ternary_operator_spaces' => true,
        'trailing_comma_in_multiline' => true,
        'trim_array_spaces' => true,
        'whitespace_after_comma_in_array' => true,
    ])
    ->setFinder($finder)
    ->setRiskyAllowed(true)
    ->setUsingCache(false); // Desabilitar cache para garantir regras aplicadas
