# Controle de Formatação Automática

Este documento explica como controlar a formatação automática no projeto para evitar mudanças desnecessárias como a remoção de espaços em expressões condicionais.

## Problema Identificado

A formatação automática estava alterando expressões como:
```php
// Antes (correto)
if(! $algumacoisa

// Depois (indesejado)
if(!$algumacoisa
```

Isso causava poluição desnecessária nos code reviews.

## Solução Implementada

### 1. Arquivo de Configuração PHP CS Fixer (`.php-cs-fixer.php`) - **OPCIONAL**

Configurado para:
- ✅ Aplicar regras PSR-12 básicas
- ✅ **CORRIGIR indentação** (`indentation_type: true`, `braces: true`)
- ✅ **Remover imports não utilizados** (`no_unused_imports: true`)
- ❌ **NÃO** forçar espaços em operadores unários (`unary_operator_spaces: false`)
- ❌ **NÃO** forçar espaços dentro de parênteses (`spaces_inside_parentheses: false`)
- ✅ Manter espaços existentes quando apropriado

### 2. Configuração VS Code/Cursor (`.vscode/settings.json`)

Configurado para:
- ❌ Desabilitar formatação automática ao salvar
- ❌ Desabilitar formatação ao colar
- ❌ Desabilitar formatação ao digitar
- ✅ Usar PHP CS Fixer quando disponível
- ✅ Preservar espaços existentes

### 3. EditorConfig Atualizado (`.editorconfig`)

Configurado para:
- ❌ **NÃO** remover espaços em branco (`trim_trailing_whitespace: false`)
- ✅ Preservar espaços em arquivos PHP
- ✅ Manter estilo de indentação consistente

## 🔄 **Formatação Automática ao Salvar (OPCIONAL)**

### **Configuração 1: Sem formatação automática (Padrão)**
```json
// .vscode/settings.json
{
    "editor.formatOnSave": false,
    "php-cs-fixer.onsave": false
}
```

### **Configuração 2: Com formatação automática ao salvar**
```json
// .vscode/settings-format-on-save.json
{
    "editor.formatOnSave": true,
    "php-cs-fixer.onsave": true
}
```

### **Configuração 3: Formatação básica ao salvar (SEM dependências)**
```json
// .vscode/settings-basic-formatting.json
{
    "editor.formatOnSave": true,
    "editor.defaultFormatter": null
}
```

### **Como ativar formatação ao salvar:**

#### **Opção A: Com PHP CS Fixer (Requer instalação)**
1. **Copiar configurações:**
   ```bash
   cp .vscode/settings-format-on-save.json .vscode/settings.json
   ```

2. **Instalar PHP CS Fixer (se quiser):**
   ```bash
   ./scripts/format-php.sh --install
   ```

3. **Reiniciar editor**

#### **Opção B: Formatação básica (SEM dependências)**
1. **Copiar configurações básicas:**
   ```bash
   cp .vscode/settings-basic-formatting.json .vscode/settings.json
   ```

2. **Reiniciar editor**

3. **Resultado:** Formatação básica sem instalar nada

### **Resultado:**
- ✅ **Ao salvar arquivo:** Formatação automática aplicada
- ✅ **Espaços preservados:** `if(! $var)` continua `if(! $var)`
- ✅ **Indentação corrigida:** Estrutura sempre consistente
- ✅ **Zero dependências:** Funciona para todos imediatamente

## Como Usar

### **Opção 1: Sem PHP CS Fixer (Recomendado para o time)**
```bash
# Verificar formatação básica (sem dependências)
./scripts/format-php.sh --check

# Ver ajuda
./scripts/format-php.sh --help
```

### **Opção 2: Com PHP CS Fixer (Para quem quiser)**
```bash
# Instalar PHP CS Fixer (OPCIONAL)
./scripts/format-php.sh --install

# Verificar formatação completa
./scripts/format-php.sh --check

# Aplicar formatação
./scripts/format-php.sh --fix
```

### **Opção 3: Formatação automática ao salvar**
```bash
# Usar configurações de formatação ao salvar
cp .vscode/settings-format-on-save.json .vscode/settings.json

# Instalar PHP CS Fixer para formatação completa
./scripts/format-php.sh --install
```

## ⚠️ **Impacto no Time**

### **Sem PHP CS Fixer (Padrão):**
- ✅ **Nenhuma dependência** adicionada
- ✅ **Funciona imediatamente** para todos
- ⚠️ **Configurações de editor** precisam ser aplicadas manualmente
- ❌ **Verificação limitada** de formatação

### **Com PHP CS Fixer (Opcional):**
- ⚠️ **Dependências adicionadas** ao projeto
- ⚠️ **Todos precisam rodar** `composer install`
- ✅ **Verificação completa** de formatação
- ✅ **Formatação automática** disponível

### **Com formatação ao salvar:**
- ✅ **Formatação automática** ao salvar arquivos
- ✅ **Zero esforço** para manter código formatado
- ⚠️ **Requer PHP CS Fixer** instalado
- ⚠️ **Pode ser intrusivo** para alguns desenvolvedores

## 🔧 **Configuração Manual do Editor**

**Para configurar VS Code/Cursor no seu computador:**

1. **Fazer pull das configurações:**
   ```bash
   git pull origin branch-name
   ```

2. **Seguir instruções:** Ver arquivo `INSTRUCOES-EDITOR.md`

3. **Ou usar script automatizado:**
   ```bash
   # Depois do pull
   ./scripts/format-php.sh --check  # Funciona sem configuração
   ```

4. **Para formatação automática ao salvar:**
   ```bash
   # Copiar configurações de formatação ao salvar
   cp .vscode/settings-format-on-save.json .vscode/settings.json
   
   # Instalar PHP CS Fixer
   ./scripts/format-php.sh --install
   ```

## Regras de Formatação Aplicadas

### ✅ **Sempre Ativo (Configurações de Editor)**
- Indentação consistente (4 espaços)
- Formatação de chaves e estruturas
- Quebras de linha no final de arquivos
- Comentários limpos
- Sintaxe de array curta `[]`

### ✅ **Com PHP CS Fixer (Opcional)**
- **Imports organizados e não utilizados removidos** - **CORRIGE automaticamente**
- Verificação completa de padrões PSR-12
- Formatação automática de todo o projeto

### ✅ **Com formatação ao salvar (Opcional)**
- **Formatação automática** ao salvar arquivos
- **Zero esforço** para manter código formatado
- **Indentação sempre corrigida** automaticamente

### ❌ **Sempre Desabilitado (Evita Mudanças Desnecessárias)**
- Espaços em operadores unários (`!`, `+`, `-`)
- Espaços em operadores binários quando já existem
- Espaços dentro de parênteses
- Remoção de espaços em branco
- Formatação automática ao salvar (quando não configurado)

## Exemplos de Código Preservado vs Corrigido

### ✅ Preservado (Espaços mantidos)
```php
// Estes estilos são preservados:
if(! $condicao) { }
if (! $condicao) { }
if ( ! $condicao ) { }

// Espaços em operadores são mantidos:
$a + $b
$a +$b
$a+ $b
$a+$b

// Parênteses mantêm espaços existentes:
function test( $param ) { }
function test($param) { }
```

### 🔧 Corrigido Automaticamente (Indentação e Estrutura)
```php
// Antes (indentação incorreta)
                if (!isset($strategies[$transferType])) {

// Depois (indentação corrigida)
        if (!isset($strategies[$transferType])) {

// Imports não utilizados são removidos automaticamente (com PHP CS Fixer)
// use App\Models\UnusedModel; // ← Removido se não usado
```

## Troubleshooting

### Se a formatação automática ainda acontecer:

1. **Verificar extensões VS Code/Cursor:**
   - Desabilitar extensões de formatação PHP conflitantes
   - Verificar se as configurações do workspace estão ativas

2. **Reiniciar editor:**
   - Fechar e abrir novamente o VS Code/Cursor
   - Recarregar a janela (Cmd+Shift+P > "Developer: Reload Window")

3. **Verificar configurações globais:**
   - VS Code pode ter configurações globais que sobrescrevem as do workspace
   - Verificar `~/.vscode/settings.json` ou configurações do usuário

### Se precisar reverter mudanças:

```bash
git checkout -- .
# Se tiver PHP CS Fixer instalado:
./scripts/format-php.sh --fix
```

## Manutenção

- **Atualizar regras:** Editar `.php-cs-fixer.php` (se instalado)
- **Configurações editor:** Editar `.vscode/settings.json`
- **Estilo geral:** Editar `.editorconfig`
- **Scripts:** Editar `scripts/format-php.sh`

## Benefícios

1. ✅ **Code Review Limpo:** Sem mudanças triviais de formatação
2. ✅ **Indentação Corrigida:** Estrutura do código sempre consistente
3. ✅ **Flexibilidade para o Time:** Cada um escolhe se quer PHP CS Fixer
4. ✅ **Configurações Editor:** Funcionam para todos imediatamente
5. ✅ **Estilo Consistente:** Regras aplicadas apenas quando necessário
6. ✅ **Controle Manual:** Formatação aplicada apenas quando solicitado
7. ✅ **Preservação de Estilo:** Espaços e formatação existentes mantidos
8. ✅ **Padrões PSR-12:** Aplicados sem ser invasivo
9. ✅ **Formatação ao Salvar:** Opcional para quem quiser automação
10. ✅ **Zero Esforço:** Para quem escolher formatação automática
