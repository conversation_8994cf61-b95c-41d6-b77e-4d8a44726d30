<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\BankAccount\QiTech\Transfer\Pix\PixKeyService;
use App\Services\BankAccount\QiTech\Transfer\Pix\PixQrCodeService;
use App\Services\BankAccount\QiTech\Transfer\Pix\PixReceiptService;
use App\Services\BankAccount\QiTech\Transfer\Pix\PixTransferService;
use App\Services\BankAccount\Transfers\Pix\PixTransferStrategyService;
use App\Services\BankAccount\Transfers\Pix\Types\ManualPixTransferService;
use App\Services\BankAccount\Transfers\Pix\Types\Transformers\ManualPixTransform;
use Illuminate\Support\ServiceProvider;

class PixTransferServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Serviços principais como singletons
        $this->app->singleton(PixTransferStrategyService::class);
        $this->app->singleton(PixTransferService::class);
        $this->app->singleton(PixKeyService::class);
        $this->app->singleton(PixQrCodeService::class);
        $this->app->singleton(PixReceiptService::class);
        
        // Serviços de estratégia específicos
        $this->app->singleton(ManualPixTransferService::class);
        $this->app->singleton(ManualPixTransform::class);

    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
