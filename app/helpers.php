<?php

use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;

if (! function_exists('getArrUfs')) {
    function getArrUfs(): array
    {
        return [
            'AC' => 'Acre',
            'AL' => 'Alagoas',
            'AP' => 'Amapá',
            'AM' => 'Amazonas',
            'BA' => 'Bahia',
            'CE' => 'Ceará',
            'DF' => 'Distrito Federal',
            'ES' => 'Espírito Santo',
            'GO' => 'Goiás',
            'MA' => 'Maranhão',
            'MT' => 'Mato Grosso',
            'MS' => 'Mato Grosso do Sul',
            'MG' => 'Minas Gerais',
            'PA' => 'Pará',
            'PB' => 'Paraíba',
            'PR' => 'Paraná',
            'PE' => 'Pernambuco',
            'PI' => 'Piauí',
            'RJ' => 'Rio de Janeiro',
            'RN' => 'Rio Grande do Norte',
            'RS' => 'Rio Grande do Sul',
            'RO' => 'Rondônia',
            'RR' => 'Roraima',
            'SC' => 'Santa Catarina',
            'SP' => 'São Paulo',
            'SE' => 'Sergipe',
            'TO' => 'Tocantins',
        ];
    }
}

if (! function_exists('addMaskEmailString')) {
    function addMaskEmailString($email): string
    {
        $username = explode('@', $email)[0];
        $domain = explode('@', $email)[1];

        if (strlen($username) <= 4) {
            $mask = str_repeat('*', strlen($username) - 1);
            $username = substr($username, 0, 1).$mask;
        } elseif (strlen($username) <= 10) {
            $mask = str_repeat('*', strlen($username) - 3);
            $username = substr($username, 0, 3).$mask;
        } else {
            $mask = str_repeat('*', strlen($username) - 6);
            $username = substr($username, 0, 3).$mask.substr($username, -3);
        }

        return $username.'@'.$domain;
    }
}

if (! function_exists('addLGPDMaskEmailString')) {
    function addLGPDMaskEmailString($email)
    {
        if (! is_string($email)) {
            return $email;
        }

        $username = explode('@', $email)[0];
        $domain = explode('@', $email)[1];

        if (strlen($username) <= 4) {
            $mask = str_repeat('*', strlen($username) - 1);
            $username = substr($username, 0, 1).$mask;
        } elseif (strlen($username) <= 10) {
            $mask = str_repeat('*', strlen($username) - 3);
            $username = substr($username, 0, 3).$mask;
        } else {
            $mask = str_repeat('*', strlen($username) - 6);
            $username = substr($username, 0, 3).$mask.substr($username, -3);
        }

        return $username.'@'.$domain;
    }
}

if (! function_exists('encryptStringApi')) {
    function encryptStringApi(string $str)
    {
        $encrypter = new \Illuminate\Encryption\Encrypter(
            \Illuminate\Support\Facades\Config::get('services.api.key'),
            \Illuminate\Support\Facades\Config::get('app.cipher')
        );

        return $encrypter->encryptString($str);
    }
}

if (! function_exists('decryptStringApi')) {
    function decryptStringApi(string $str)
    {
        $encrypter = new \Illuminate\Encryption\Encrypter(
            \Illuminate\Support\Facades\Config::get('services.api.key'),
            \Illuminate\Support\Facades\Config::get('app.cipher')
        );

        return $encrypter->decryptString($str);
    }
}

if (! function_exists('getArrEstadoCivil')) {
    function getArrEstadoCivil()
    {
        return [
            'Solteiro',
            'Casado',
            'Divorciado',
            'Separado',
            'Viúvo',
            'União estável',
        ];
    }
}

if (! function_exists('getArrConheciaCury')) {
    function getArrConheciaCury()
    {
        return [
            'Sim',
            'Não',
        ];
    }
}

if (! function_exists('getArrTipoAtendimento')) {
    function getArrTipoAtendimento()
    {
        return [
            'Indicação',
            'Retorno',
            'Espontâneo',
            'Ligação',
        ];
    }
}

if (! function_exists('getArrObjetivoCompra')) {
    function getArrObjetivoCompra()
    {
        return [
            'Moradia',
            'Aluguel',
            'Investimento',
        ];
    }
}

if (! function_exists('getArrComoConheceuImovel')) {
    function getArrComoConheceuImovel()
    {
        return [
            'Rádio',
            'Internet',
            'TV',
            'Jornal',
            'Whatsapp',
            'Panfleto',
            'Outdoor',
            'Redes Sociais',
            'E-mail marketing',
            'SMS',
            'Outros',
        ];
    }
}

/*
 * Rádio;Internet;TV;Jornal;Whatsapp;Panfleto;Outdoor;Redes Sociais;E-mail marketing;SMS;Outros
 */

if (! function_exists('getArrCanalCorretor')) {
    function getArrCanalCorretor()
    {
        return [
            // 'PDV',
            // 'On-line',
            // 'Cia Cury',
            'Cury Vendas',
            'CIA/Imobiliária',
            'Autônomo/Parceiro',
        ];
    }
}

if (! function_exists('getArrCamisasCorretorTamanho')) {
    function getArrCamisasCorretorTamanho()
    {
        return [
            'P',
            'M',
            'G',
            '2G',
            '3G',
        ];
    }
}

if (! function_exists('getArrRendaFamiliar')) {
    function getArrRendaFamiliar()
    {
        return [
            '1 salário mínimo',
            '2 salários mínimos',
            '3 salários mínimos',
            '4 salários mínimos',
            '5 salários mínimos ou mais',
        ];
    }
}

if (! function_exists('getArrRelacionamentoCury')) {
    function getArrRelacionamentoCury()
    {
        return [
            [
                'value' => 'Cury Vendas',
                'description' => 'Corretor de salão/plantão vinculado a um gerente PDV',
                'text' => 'Quero ser um Corretor Cury Vendas',
            ],
            [
                'value' => 'CIA/Imobiliária',
                'description' => 'Corretor vinculado a uma imobiliária parceira da Cury Vendas',
                'text' => 'Sou de uma Imobiliária Parceira',
            ],
            [
                'value' => 'Autônomo/Parceiro',
                'description' => 'Corretor vinculado a um gerente de parcerias (sem uma imobiliária parceira)',
                'text' => 'Quero ser um Parceiro Autônomo',
            ],
        ];
    }
}

if (! function_exists('getAppCuryHeaders')) {
    function getAppCuryHeaders(): array
    {
        $arrHeadersKey = [
            'appcury-base-os',
            'appcury-system-version',

            'appcury-brand',
            'appcury-device',
            'appcury-device-id',

            'appcury-unique-id',

            'appcury-readable-version',

            'appcury-is-airplane-mode',
            'appcury-is-emulator',
            'appcury-is-location-enabled',
        ];

        $arrHeaders = [];
        $request = request();
        $headers = $request->headers->all();

        foreach ($arrHeadersKey as $key) {
            $arrHeaders[str_replace('appcury-', '', $key)] = $headers[$key][0] ?? null;
        }

        if (! count(array_filter($arrHeaders))) {
            return [];
        }

        $arrHeaders['ip'] = $request->getClientIp();

        return $arrHeaders;
    }
}

if (! function_exists('formatCnpj')) {
    function formatCnpj($value)
    {
        $cleanValue = preg_replace('/\D/', '', $value);
        $maskedValue = preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $cleanValue);

        return $maskedValue;
    }
}

if (! function_exists('formatCpf')) {
    function formatCpf($value)
    {
        $cleanValue = preg_replace('/\D/', '', $value);
        $maskedValue = preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $cleanValue);

        return $maskedValue;
    }
}

if (! function_exists('identifyCpfCnpj')) {
    function identifyCpfCnpj($value)
    {
        $cleanValue = preg_replace('/\D/', '', $value);
        $length = strlen($cleanValue);

        if ($length === 11) {
            return 'CPF';
        } elseif ($length === 14) {
            return 'CNPJ';
        }

        return null;
    }
}

if (! function_exists('completarCpfComZeros')) {
    function completarCpfComZeros($value)
    {
        $cleanValue = preg_replace('/\D/', '', $value);
        $length = strlen($cleanValue);

        if ($length === 0) {
            return $value;
        }

        while ($length < 11) {
            $cleanValue = "0{$cleanValue}";
            $length = strlen($cleanValue);
        }

        return $cleanValue;
    }
}

if (! function_exists('completarCnpjComZeros')) {
    function completarCnpjComZeros($value)
    {
        $cleanValue = preg_replace('/\D/', '', $value);
        $length = strlen($cleanValue);

        if ($length === 0) {
            return $value;
        }

        while ($length < 14) {
            $cleanValue = "0{$cleanValue}";
            $length = strlen($cleanValue);
        }

        return $cleanValue;
    }
}

if (! function_exists('generateSalesforceFakeId')) {
    function generateSalesforceFakeId()
    {
        return uniqid('a1E1U000002');
    }
}

if (! function_exists('phone_to_e164')) {
    function phone_to_e164(?string $phone = null)
    {
        if (empty($phone)) {
            return null;
        }

        $phoneUtil = PhoneNumberUtil::getInstance();
        try {
            $phoneE164Proto = $phoneUtil->parse($phone, 'BR');

            return $phoneUtil->format($phoneE164Proto, PhoneNumberFormat::E164);
        } catch (NumberParseException $e) {
            \Illuminate\Support\Facades\Log::info('phone_to_e164: Erro ao converter telefone: '.$phone);

            return null;
        }
    }
}

if (! function_exists('isServerOverloaded')) {
    function isServerOverloaded(): bool
    {
        $loadAverage = sys_getloadavg();
        $overloadedValue = config('services.server.load-average-overloaded');

        return ($loadAverage[0] ?? 0) >= $overloadedValue;
    }
}


if (!function_exists('getOnlyNumbers')) {
    function getOnlyNumbers($value): string
    {
        return preg_replace('/\D/', '', (string) $value);
    }
}
