<?php

declare(strict_types=1);

namespace App\Http\Controllers\BankAccount\Transfers\Pix;

use App\Enums\BankAccount\Transactions\Pix\PixTransferTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\BankAccount\Transfers\Pix\AddFavoritePixTransferRequest;
use App\Http\Requests\BankAccount\Transfers\Pix\ConfirmPixTransferRequest;
use App\Http\Requests\BankAccount\Transfers\Pix\ExecutePixTransferRequest;
use App\Http\Requests\BankAccount\Transfers\Pix\ManualPixTransferRequest;
use App\Http\Resources\BankAccount\Transfers\Pix\ManualPixStatusResource;
use App\Http\Resources\BankAccount\Transfers\Pix\ManualPixTransferResource;
use App\Services\BankAccount\Transfers\Pix\PixTransferStrategyService;
use App\Models\BankAccount;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Arr;

class TransferPixController extends Controller
{
    public function __construct(
        private readonly PixTransferStrategyService $pixTransferStrategyService
    ) {
    }

    /**
     * Get the bank account from the request.
     * The bankAccount is merged into the request by the HasBankAccount middleware.
     *
     * @param Request $request
     * @return BankAccount
     */
    private function getBankAccount(Request $request): BankAccount
    {
        return $request->bankAccount;
    }

    /**
     * Create a manual PIX transfer.
     *
     * @param ManualPixTransferRequest $request
     * @return JsonResponse
     */
    public function createManualTransfer(ManualPixTransferRequest $request): JsonResponse
    {
        $bankAccount = $this->getBankAccount($request);

        $transferData = $request->validated();
        $transferData['bank_account_id'] = $bankAccount->id;
        $transferData['user_id'] = $bankAccount->user_id;
        $transferData['account_key'] = Arr::get($bankAccount, 'account_info.account_key');

        $transaction = $this->pixTransferStrategyService->createTransfer(
            PixTransferTypeEnum::MANUAL,
            $transferData
        );

        return response()->json(new ManualPixTransferResource($transaction));

    }

    /**
     * Execute a PIX transfer.
     *
     * @param Request $request
     * @param string $requestControlKey
     * @return JsonResponse
     */
    public function executeTransfer(ExecutePixTransferRequest $request, string $requestControlKey): JsonResponse
    {

        $executionData = $request->validated();
        $executionData['request_control_key'] = $requestControlKey;

        $bankAccount = $this->getBankAccount($request);
        $executionData['approver_document_number'] = $bankAccount->user->cpf;

        $this->pixTransferStrategyService->executeTransfer($executionData);

        return response()->json([], 200);


    }

    /**
     * Confirm a PIX transfer.
     *
     * @param Request $request
     * @param string $requestControlKey
     * @return JsonResponse
     */
    public function confirmTransfer(ConfirmPixTransferRequest $request, string $requestControlKey): JsonResponse
    {

        $confirmationData = $request->validated();
        $bankAccount = $this->getBankAccount($request);
        $confirmationData['account_key'] = $bankAccount->account_info['account_key'];
        $confirmationData['request_control_key'] = $requestControlKey;

        $this->pixTransferStrategyService->confirmTransfer($confirmationData);

        return response()->json([], 200);
    }

    /**
     * Get PIX transfer status.
     *
     * @param Request $request
     * @param string $requestControlKey
     * @return JsonResponse
     */
    public function statusTransfer(Request $request, string $requestControlKey): JsonResponse
    {
        try {

            $statusData['request_control_key'] = $requestControlKey;

            $status = $this->pixTransferStrategyService->statusTransfer($statusData);

            return response()->json(new ManualPixStatusResource($status));

        } catch (\Exception $e) {
            Log::error('Error getting PIX transfer status', [
                'error' => $e->getMessage(),
                'request_control_key' => $requestControlKey,
                'user_id' => $user_id ?? null,
            ]);

            return response()->json([
                'message' => 'Erro ao consultar status da transferência',
            ], 500);
        }
    }

    /**
     * Get PIX transfer receipt (placeholder).
     *
     * @param Request $request
     * @param string $requestControlKey
     * @return JsonResponse
     */
    public function getTransferReceipt(Request $request, string $requestControlKey): JsonResponse
    {
        $bankAccount = $this->getBankAccount($request);
        $user_id = $bankAccount->user_id;
        try {

            return response()->json([
                'message' => 'Comprovante será implementado em versão futura',
                'request_control_key' => $requestControlKey,
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting PIX transfer receipt', [
                'error' => $e->getMessage(),
                'request_control_key' => $requestControlKey,
                'user_id' => $user_id,
            ]);

            return response()->json([
                'message' => 'Erro ao gerar comprovante',
            ], 500);
        }
    }

    public function addToFavorites(AddFavoritePixTransferRequest $request, string $requestControlKey): JsonResponse
    {
        //TODO
        // TRATAR ADD FAVORITE
        $bankAccount = $this->getBankAccount($request);
        $user_id = $bankAccount->user_id;
        try {
            return response()->json([], 200);

        } catch (\Exception $e) {
            Log::error('Error adding favorite transfer', [
                'error' => $e->getMessage(),
                'request_control_key' => $requestControlKey,
                'user_id' => $user_id,
            ]);

            return response()->json([
                'message' => 'Erro ao adicionar transferência aos favoritos',
            ], 500);
        }
    }

}
