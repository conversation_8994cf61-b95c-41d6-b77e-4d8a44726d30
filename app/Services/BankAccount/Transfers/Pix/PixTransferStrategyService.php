<?php

declare(strict_types=1);

namespace App\Services\BankAccount\Transfers\Pix;

use App\Enums\BankAccount\Transactions\Pix\PixTransferTypeEnum;
use App\Exceptions\BankAccount\Transfers\Pix\CreatePixException;
use App\Models\BankAccount;
use App\Models\BankAccountTransaction;
use App\Services\BankAccount\Transfers\Pix\Types\Contracts\PixTransferInterface;
use App\Services\BankAccount\Transfers\Pix\Types\ManualPixTransferService;
use Illuminate\Support\Facades\Log;

class PixTransferStrategyService
{
    private array $strategies = [];
    private bool $isInitialized = false;
    public function __construct(
        private readonly ManualPixTransferService $manualPixTransferService
    ) {
        
    }

    private function getStrategies(): array
    {
        if (!$this->isInitialized) {
            $this->registerStrategies();
            $this->isInitialized = true;
        }

        return $this->strategies;
    }

    private function registerStrategies(): void
    {
        $this->strategies[PixTransferTypeEnum::MANUAL->value] = $this->manualPixTransferService;

        // Próximos:
        // $this->strategies[PixTransferTypeEnum::KEY->value] = $this->keyPixTransferService;
        // $this->strategies[PixTransferTypeEnum::STATIC_QR_CODE->value] = $this->staticQrCodePixTransferService;
        // $this->strategies[PixTransferTypeEnum::DYNAMIC_QR_CODE->value] = $this->dynamicQrCodePixTransferService;
    }

    public function createTransfer(PixTransferTypeEnum $transferType, array $transferData): BankAccountTransaction
    {
        try {

            $strategy = $this->getStrategy(transferType: $transferType->value);

            return $strategy->createTransfer($transferData);

        } catch (CreatePixException $e) {
            Log::info('Error creating manual PIX transfer', [
                'error' => $e->getMessage(),
            ]);

            throw new CreatePixException(
                'Erro ao criar transferência PIX: ' . $e->getMessage(),
                422,
                $e->getContext()
            );
        }
    }

    public function executeTransfer(array $executionData): BankAccountTransaction
    {
        $transaction = $this->findTransactionByControlKey($executionData['request_control_key']);

        $strategy = $this->getStrategy($transaction->pix_transfer_type);

        Log::info('Executing PIX transfer', [
            'request_control_key' => $executionData['request_control_key'],
            'transfer_type' => $transaction->pix_transfer_type,
        ]);

        return $strategy->executeTransfer($transaction, $executionData);
    }


    public function confirmTransfer(array $confirmationData): BankAccountTransaction
    {
        $transaction = $this->findTransactionByControlKey($confirmationData['request_control_key']);

        $strategy = $this->getStrategy($transaction->pix_transfer_type);

        Log::info('Confirming PIX transfer', [
            'confirmationData' => $confirmationData,
            'transaction' => $transaction,
        ]);

        return $strategy->confirmTransfer($transaction, $confirmationData);
    }


    public function statusTransfer(array $statusData): BankAccountTransaction
    {
        $transaction = $this->findTransactionByControlKey($statusData['request_control_key']);

        $strategy = $this->getStrategy($transaction->pix_transfer_type);

        return $strategy->statusTransfer($transaction);
    }

    private function getStrategy(string $transferType): PixTransferInterface
    {
        $strategies = $this->getStrategies();

        if (!isset($strategies[$transferType])) {
            throw new \InvalidArgumentException(
                "PIX transfer strategy not implemented for type: {$transferType}"
            );
        }

        return $strategies[$transferType];
    }

    public function findTransactionByControlKey(string $requestControlKey): BankAccountTransaction
    {
        $transaction = BankAccountTransaction::where('request_control_key', $requestControlKey)
            ->first();

        if (!$transaction) {
            throw new \Exception('Transação não encontrada');
        }

        return $transaction;
    }

}
