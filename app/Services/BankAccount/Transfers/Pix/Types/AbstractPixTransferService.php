<?php

declare(strict_types=1);

namespace App\Services\BankAccount\Transfers\Pix\Types;

use App\Enums\BankAccount\Transactions\Pix\PixTransferDirectionEnum;
use App\Enums\BankAccount\Transactions\Pix\PixTransferStatusEnum;
use App\Enums\BankAccount\Transactions\Pix\PixTransferTypeEnum;
use App\Enums\BankAccount\Transactions\TfaTypeEnum;
use App\Enums\BankAccount\Transactions\TransactionTypeEnum;
use App\Exceptions\BankAccount\QiTech\QiTechApiException;
use App\Exceptions\BankAccount\Transfers\Pix\ConfirmPixException;
use App\Exceptions\BankAccount\Transfers\Pix\CreatePixException;
use App\Exceptions\BankAccount\Transfers\Pix\ExecutePixException;
use App\Exceptions\BankAccount\Transfers\Pix\StatusPixException;
use App\Models\BankAccount;
use App\Models\BankAccountTransaction;
use App\Services\BankAccount\FinancialInstitutionService;
use App\Services\BankAccount\QiTech\Transfer\Pix\PixKeyService;
use App\Services\BankAccount\QiTech\Transfer\Pix\PixTransferService;
use App\Services\BankAccount\Transfers\Pix\Types\Contracts\PixTransferInterface;
use App\Services\BankAccount\Transfers\Pix\Types\Contracts\PixTransformInterface;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

abstract class AbstractPixTransferService implements PixTransferInterface
{
    protected PixTransformInterface $pixTransform;

    public function __construct(
        protected readonly PixTransferService $pixTransferService,
        protected readonly PixKeyService $pixKeyService
    ) {
    }
    protected function setPixTransform(PixTransformInterface $pixTransform): void
    {
        $this->pixTransform = $pixTransform;
    }

    public function createTransfer(array $transferData): BankAccountTransaction
    {
        try {
            $transaction = BankAccountTransaction::create($transferData);

            Log::info('Manual PIX transfer created', [
                'transaction' => $transaction->toArray()
            ]);

            return $transaction;

        } catch (\Exception $e) {
            Log::error('Error creating manual PIX transfer', [
                'error' => $e->getMessage(),
                'user_id' => $transferData['user_id'],
            ]);
            throw new CreatePixException(
                'Erro ao criar transferência PIX: ' . $e->getMessage(),
                422,
                ['user_id' => $transferData['user_id'], 'exception' => $e]
            );

        }
    }

    public function executeTransfer(BankAccountTransaction $transaction, array $executionData): BankAccountTransaction
    {
        try {
            $transformedExecutionData = $this->pixTransform->execute($transaction, $executionData);

            $response = $this->pixTransferService->executeTransfer($transformedExecutionData)->getData();
            Log::info('API QITECH PIX transfer executed', [
                'request_control_key' => $executionData['request_control_key'],
                'transformedExecutionData' => $transformedExecutionData,
                'response' => $response,
            ]);

            $transformedExecutionSucessData = $this->pixTransform->sucessExecution($response, $executionData);

            Log::info('API QITECH PIX transfer updated', [
                'transformedExecutionSucessData' => $transformedExecutionSucessData
            ]);

            $transaction->update($transformedExecutionSucessData);

            Log::info('API QITECH PIX transfer updated', [
                'request_control_key' => $executionData['request_control_key'],
                'transaction' => $transaction,
            ]);

            return $transaction;
        } catch (QiTechApiException $e) {
            Log::error('Error executing PIX transfer', [
                'error' => $e->getMessage(),
                'executionData' => $executionData,
                'transaction' => $transaction->toArray(),
            ]);

            throw new ExecutePixException(
                'Erro ao executar transferência PIX: ' . $e->getMessage(),
                422,
            );
        } catch (\Exception $e) {
            Log::error('Error updating PIX transfer', [
                'error' => $e->getMessage(),
                'transaction' => $transaction,
            ]);

            throw new ExecutePixException(
                'Erro ao salvar dados da transferência PIX: ' . $e->getMessage(),
                422,
            );
        }

    }

    public function confirmTransfer(BankAccountTransaction $transaction, array $confirmationData): BankAccountTransaction
    {
        try {
            $transformedConfirmationData = $this->pixTransform->confirm($transaction, $confirmationData);
            
            Log::info('Transformed confirmation data', [
                'transformedConfirmationData' => $transformedConfirmationData,
                'transaction' => $transaction,
            ]);

            $response = $this->pixTransferService->confirmTransfer($transformedConfirmationData['account_key'], $transaction->pix_transfer_key, $transformedConfirmationData['token'])->getData();

            Log::info('API QITECH PIX transfer confirmed', [
                'transformedConfirmationData' => $transformedConfirmationData,
                'transaction' => $transaction,
                'response' => $response,
            ]);

            $transformedConfirmationSucessData = $this->pixTransform->sucessConfirmation($response);

            Log::info('API QITECH PIX transfer updated', [
                'transformedConfirmationSucessData' => $transformedConfirmationSucessData
            ]);

            $transaction->update($transformedConfirmationSucessData);

            Log::info('API QITECH PIX transfer updated', [
                'request_control_key' => $confirmationData['request_control_key'],
                'transaction' => $transaction,
            ]);

            return $transaction;

        } catch (QiTechApiException $e) {
            Log::error('Error confirming PIX transfer', [
                'error' => $e->getMessage(),
                'response_body' => $e->getResponseBody(),
                'transformedConfirmationData' => $transformedConfirmationData,
                'transaction' => $transaction,
            ]);

            $transaction->update([
                "updated_at" => now(),
                // "api_updated_at" => $response['created_at'],
                'error' => $e->getResponseBody(),
            ]);

            throw new ConfirmPixException(
                'Erro ao confirmar transferência PIX: ' . $e->getMessage(),
                422,
                $e->getResponseBody()
            );
        }
    }

    public function statusTransfer(BankAccountTransaction $transaction): BankAccountTransaction
    {
        try {
            $statusResponse = $this->pixTransform->status($transaction);

            $response = $this->pixTransferService->statusTransfer(
                $transaction->account_key, 
                $transaction->pix_transfer_key, 
                PixTransferDirectionEnum::OUTGOING->value
            )->getData();

        Log::info('API QITECH PIX transfer status', [
            'response' => $response,
        ]);

        $transaction->update($statusResponse);

        Log::info('PIX transfer updated', [
            'transaction' => $transaction,
        ]);

        return $transaction;
        } catch (\Exception $e) {
            Log::error('Error getting PIX transfer status', [
                'error' => $e->getMessage(),
                'transaction' => $transaction,
            ]);

            throw new StatusPixException(
                'Erro ao consultar status da transferência PIX: ' . $e->getMessage(),
                422,
            );
        }
    }


    public function addFavorite()
    {
        //TODO
        // TRATAR ADD FAVORITE
    }




}
