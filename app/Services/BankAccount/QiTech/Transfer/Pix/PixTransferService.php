<?php

declare(strict_types=1);

namespace App\Services\BankAccount\QiTech\Transfer\Pix;

use App\Models\BankAccountTransaction;
use App\Services\BankAccount\QiTech\Core\AbstractQiTechApiService;
use App\Services\BankAccount\QiTech\Core\QiTechResponse;
use Illuminate\Support\Facades\Log;

/**
 * Serviço para gerenciar operações de Movimentações PIX na QiTech
 *
 * Responsável por:
 * - Realizar transferência Pix
 * - Devolução de um Pix recebido
 * - Consultar transferências
 * - Listar transferências
 */
final class PixTransferService extends AbstractQiTechApiService
{
    /**
     * Realiza uma transferência PIX
     *
     * @see https://docs.qitech.com.br/documentation/baas/pix/realizar_transferencia
     */
    public function executeTransfer(array $executionData): QiTechResponse
    {
        $transformedData = $this->dataTransformerContext->transform($executionData, 'pix_transfer');

        $endpoint = '/account/' . $executionData['account_key'] . '/pix_transfer';

        return $this->apiService->sendBaaSRequest($endpoint, 'POST', $transformedData);
    }

    /**
     * Aprova transação com Autenticação de Dois Fatores
     *
     * @param string $accountKey Chave única de identificação da conta
     * @param string $pixTransferKey Chave única de identificação da transação pix
     * @param array $data Dados contendo o token de validação
     */
    public function confirmTransfer(string $accountKey, string $pixTransferKey, array $data): QiTechResponse
    {
        $endpoint = "/account/{$accountKey}/pix_transfer/{$pixTransferKey}/validate_token";

        return $this->apiService->sendBaaSRequest($endpoint, 'PUT', $data);
    }
    /**
     * Consulta uma transferência PIX específica
     *
     * @see https://docs.qitech.com.br/documentation/baas/pix/consultar_transferencias
     *
     * Enumeradores pix_transfer_direction
     * incoming - Transferência Pix de entrada
     * outgoing - Transferência Pix de saída
     */
    public function statusTransfer(string $account_key, string $pix_transfer_key, string $pix_transfer_direction): QiTechResponse
    {
        $endpoint = "/account/{$account_key}/pix_transfer/{$pix_transfer_key}/{$pix_transfer_direction}";

        return $this->apiService->sendBaaSRequest($endpoint, 'GET');
    }

    /**
     * Lista transferências PIX de uma conta
     *
     * @see https://docs.qitech.com.br/documentation/baas/pix/listar_transferencias
     */
    public function listTransfers(string $accountKey, ?array $filters = null): QiTechResponse
    {
        $params = ['account_key' => $accountKey];

        if ($filters) {
            $params = array_merge($params, $filters);
        }

        $endpoint = "/account/{$accountKey}/pix_transfers";

        return $this->apiService->sendBaaSRequest($endpoint, 'GET');
    }

    /**
     * Recupera token enviado para Autenticação de Dois Fatores
     *
     * Para transações pix individuais e em lote de parceiros integradores
     * com configuração de autenticação de dois fatores
     */
    public function getTwoFactorToken(string $transactionRequestKey): QiTechResponse
    {
        $endpoint = "/mock/2fa/transaction_request/{$transactionRequestKey}";

        return $this->apiService->sendBaaSRequest($endpoint, 'GET');
    }

}
