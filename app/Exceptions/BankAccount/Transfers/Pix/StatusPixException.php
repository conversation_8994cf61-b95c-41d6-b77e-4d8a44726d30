<?php

declare(strict_types=1);

namespace App\Exceptions\BankAccount\Transfers\Pix;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Log;

final class StatusPixException extends HttpResponseException
{
    private array $context;

    public function __construct(
        string $message,
        int $statusCode = 422,
        array $context = []
    ) {
        $this->context = $context;
        parent::__construct(
            response()->json(
                [
                    'message' => $message,
                    'context' => $context,
                ],
                $statusCode
            )
        );
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function getStatusCode(): int
    {
        return $this->getCode();
    }

    public function getErrorCode(): string
    {
        return $this->context['error_code'] ?? '';
    }

    public function getErrorType(): string
    {
        return $this->context['error_type'] ?? '';
    }

    /**
     * Códigos de erro específicos para consulta de status PIX da QiTech
     */
    public const ERROR_CODES = [
        // Erros de consulta de transferência
        'PXT000075' => 'Não foram fornecidos uma pix transfer key ou end to end id.',
        'PXT000023' => 'Transferência PIX de saída não foi encontrada.',
        'PIT000001' => 'Usuário não tem autorização para fazer essa transação.',

        // Erros gerais de PIX
        'QIT000001' => 'Erro de Schema.',
        'PIT000003' => 'Saldo de conta insuficiente para a transferência e a taxa.',
        'PIT000004' => 'O total da transferência é superior ao limite.',
        'PXT000003' => 'Conta está fechada.',
        'PXT000004' => 'Conta não encontrada.',
        'PXT000010' => 'Conta está bloqueada.',
        'PXT000018' => 'Transferência original da devolução não foi encontrada.',
        'PXT000033' => 'A conta de destino não pode ser a conta de origem.',
        'PXT000041' => 'Qr Code não encontrado.',
        'PXT000048' => 'Emoji não é permitido na mensagem pix.',
        'PXT000053' => 'Qr Code já Pago.',
        'PXT000060' => 'Conta inexistente no banco de destino.',
        'PXT000061' => 'End to end id inválido. Uma transação pix com o identificador único já foi registrada.',
        'PXT000079' => 'Saldo de conta de cobrança insuficiente para a taxa.',
        'PXT000083' => 'Pix rejeitado.',
        'PXT000103' => 'request_control_key não foi aceito por não ser uma palavra uuid v4 válida.',
        'PXT000104' => 'O valor de transação não é válido. Deve ser um valor positivo com no máximo duas casas decimais.',
        'PXT000105' => 'O end_to_end_id enviado não é válido.',
        'PXT000108' => 'Conta de cobrança encerrada ou bloqueada.',
        'PXT000109' => 'request_control_key já utilizada.',
        'PXT000115' => 'Saldo de conta insuficiente para a transferência e a taxa.',
        'PXT000118' => 'O requisitante enviou uma alias key no entanto não é um participante do pix indireto.',
        'PXT000128' => 'Chave Pix enviada não condiz com consulta. Verifique se end_to_end_id enviado está correto.',
        'PXT000129' => 'Mensagem rejeitada pela SPI-ICOM.',
        'PXT000130' => 'Controle de timeout no SPI.',
        'PXT000131' => 'Transação interrompida devido a erro no PSP do Recebedor.',
        'PXT000132' => 'Número da conta de destino é inexistente ou inválido.',
        'PXT000133' => 'A conta de destino encontra-se bloqueada.',
        'PXT000134' => 'A conta de destino encontra-se encerrada.',
        'PXT000135' => 'A conta de destino não suporta este tipo de transação.',
        'PXT000136' => 'Participante direto do SPI não é liquidante do PSP do Pagador / Recebedor.',
        'PXT000137' => 'Ordem de pagamento com valor zero.',
        'PXT000138' => 'Saldo insuficiente na conta PI do pagador.',
        'PXT000139' => 'Valor de devolução acima do valor de pagamento correspondente.',
        'PXT000140' => 'Quantidade de transações inválida.',
        'PXT000141' => 'CPF/CNPJ do usuário recebedor não é compatível com o titular da conta de destino.',
        'PXT000142' => 'CPF/CNPJ da conta de destino está incorreto.',
        'PXT000143' => 'Elemento da mensagem incorreto.',
        'PXT000144' => 'Ordem de pagamento foi rejeitada pelo banco recebedor.',
        'PXT000145' => 'Participante que assinou a mensagem não é autorizado a realizar a operação na conta PI debitada.',
        'PXT000146' => 'Data e Hora do envio da mensagem inválida.',
        'PXT000147' => 'Erro no processamento do pagamento (erro genérico).',
        'PXT000148' => 'Identificador da operação mal formatado.',
        'PXT000149' => 'Número ISPB do PSP do Pagador é inválido ou inexistente.',
        'PXT000150' => 'Número ISPB do banco recebedor é inválido ou inexistente.',
        'PXT000151' => 'Tipo incorreto para a conta transacional especificada.',
        'PXT000152' => 'O end_to_end_id já foi utilizado.',
        'PXT000153' => 'O tipo de conta destino não pode receber transações PIX.',
        'PXT000154' => 'Número ISPB é inválido ou inexistente.',
        'PXT000155' => 'Valor de pagamento/devolução acima do permitido para a conta de destino creditada.',
        'PXT000156' => 'QR Code rejeitado pelo PSP do usuário recebedor.',
        'PXT000157' => 'Não pode enviar a mensagem para a ICOM depois de 3 tentativas.',
        'PXT000158' => 'O valor do pagamento diverge do valor esperado.',
        'PXT000167' => 'Requester não possui permissão de realizar transações pix através deste endpoint.',
    ];

    /**
     * Mapeamento de códigos de erro para códigos HTTP
     */
    public const HTTP_STATUS_CODES = [
        'PXT000075' => 400,
        'PXT000023' => 404,
        'PIT000001' => 403,
        'QIT000001' => 400,
        'PIT000003' => 400,
        'PIT000004' => 400,
        'PXT000003' => 400,
        'PXT000004' => 404,
        'PXT000010' => 400,
        'PXT000018' => 404,
        'PXT000033' => 400,
        'PXT000041' => 404,
        'PXT000048' => 400,
        'PXT000053' => 400,
        'PXT000060' => 400,
        'PXT000061' => 400,
        'PXT000079' => 400,
        'PXT000083' => 400,
        'PXT000103' => 406,
        'PXT000104' => 400,
        'PXT000105' => 406,
        'PXT000108' => 400,
        'PXT000109' => 400,
        'PXT000115' => 400,
        'PXT000118' => 400,
        'PXT000128' => 400,
        'PXT000129' => 400,
        'PXT000130' => 408,
        'PXT000131' => 400,
        'PXT000132' => 400,
        'PXT000133' => 400,
        'PXT000134' => 400,
        'PXT000135' => 400,
        'PXT000136' => 400,
        'PXT000137' => 400,
        'PXT000138' => 400,
        'PXT000139' => 400,
        'PXT000140' => 400,
        'PXT000141' => 400,
        'PXT000142' => 400,
        'PXT000143' => 400,
        'PXT000144' => 403,
        'PXT000145' => 403,
        'PXT000146' => 400,
        'PXT000147' => 400,
        'PXT000148' => 400,
        'PXT000149' => 400,
        'PXT000150' => 400,
        'PXT000151' => 400,
        'PXT000152' => 400,
        'PXT000153' => 400,
        'PXT000154' => 400,
        'PXT000155' => 400,
        'PXT000156' => 400,
        'PXT000157' => 503,
        'PXT000158' => 400,
        'PXT000167' => 403,
    ];

    /**
     * Cria uma exception baseada no código de erro da QiTech para consulta de status PIX.
     */
    public static function fromQiTechError(string $errorCode, ?int $statusCode = null): self
    {
        $message = self::ERROR_CODES[$errorCode] ?? 'Erro desconhecido na consulta de status PIX.';
        $httpStatusCode = $statusCode ?? self::HTTP_STATUS_CODES[$errorCode] ?? 422;

        $context = [
            'error_code' => $errorCode,
            'error_type' => 'pix_status_query_error',
            'service' => 'PIX Status Query'
        ];

        Log::error('QiTech PIX Status Query Error', [
            'error_code' => $errorCode,
            'message' => $message,
            'status_code' => $httpStatusCode,
            'context' => $context
        ]);

        return new self($message, $httpStatusCode, $context);
    }

    /**
     * Cria uma exception baseada na resposta de erro da API QiTech para consulta de status PIX.
     */
    public static function fromApiResponse(array $responseData, int $statusCode): self
    {
        $errorCode = $responseData['code'] ?? 'UNKNOWN_CODE';
        $title = $responseData['title'] ?? '';
        $description = $responseData['description'] ?? '';
        $translation = $responseData['translation'] ?? '';
        $extraFields = $responseData['extra_fields'] ?? [];

        // Usar a tradução em português se disponível, senão usar a descrição em inglês
        $message = $translation ?: $description;

        // Se não há mensagem específica, usar a mensagem padrão do código de erro
        if (empty($message)) {
            $message = self::ERROR_CODES[$errorCode] ?? 'Erro desconhecido na consulta de status PIX.';
        }

        $context = [
            'error_code' => $errorCode,
            'error_type' => 'pix_status_query_error',
            'title' => $title,
            'description' => $description,
            'translation' => $translation,
            'extra_fields' => $extraFields,
            'service' => 'PIX Status Query'
        ];

        Log::error('QiTech PIX Status Query API Error', [
            'response_data' => $responseData,
            'status_code' => $statusCode,
            'context' => $context
        ]);

        return new self($message, $statusCode, $context);
    }

    /**
     * Cria uma exception para transferência PIX não encontrada.
     */
    public static function fromTransferNotFound(string $pixTransferKey): self
    {
        $message = "Transferência PIX de saída com chave {$pixTransferKey} não foi encontrada.";

        $context = [
            'error_code' => 'PXT000023',
            'error_type' => 'transfer_not_found',
            'pix_transfer_key' => $pixTransferKey,
            'service' => 'PIX Status Query'
        ];

        Log::warning('PIX Transfer Not Found', $context);

        return new self($message, 404, $context);
    }

    /**
     * Cria uma exception para usuário não autorizado.
     */
    public static function fromUnauthorizedUser(): self
    {
        $message = 'Usuário não tem autorização para fazer essa transação.';

        $context = [
            'error_code' => 'PIT000001',
            'error_type' => 'unauthorized_user',
            'service' => 'PIX Status Query'
        ];

        Log::warning('Unauthorized PIX Status Query', $context);

        return new self($message, 403, $context);
    }

    /**
     * Cria uma exception para parâmetros inválidos na consulta.
     */
    public static function fromInvalidParameters(): self
    {
        $message = 'Não foram fornecidos uma pix transfer key ou end to end id.';

        $context = [
            'error_code' => 'PXT000075',
            'error_type' => 'invalid_parameters',
            'service' => 'PIX Status Query'
        ];

        Log::error('Invalid PIX Status Query Parameters', $context);

        return new self($message, 400, $context);
    }

    /**
     * Cria uma exception genérica para erros de consulta de status PIX.
     */
    public static function fromGenericError(?string $message = null, int $statusCode = 422): self
    {
        $errorMessage = $message ?? 'Erro desconhecido na consulta de status PIX.';

        $context = [
            'error_code' => 'UNKNOWN_CODE',
            'error_type' => 'generic_error',
            'service' => 'PIX Status Query'
        ];

        Log::error('Generic PIX Status Query Error', [
            'message' => $errorMessage,
            'status_code' => $statusCode,
            'context' => $context
        ]);

        return new self($errorMessage, $statusCode, $context);
    }

    /**
     * Verifica se o código de erro é conhecido.
     */
    public static function isKnownErrorCode(string $errorCode): bool
    {
        return array_key_exists($errorCode, self::ERROR_CODES);
    }

    /**
     * Obtém a mensagem de erro para um código específico.
     */
    public static function getErrorMessage(string $errorCode): ?string
    {
        return self::ERROR_CODES[$errorCode] ?? null;
    }

    /**
     * Obtém o código HTTP para um código de erro específico.
     */
    public static function getHttpStatusCode(string $errorCode): int
    {
        return self::HTTP_STATUS_CODES[$errorCode] ?? 422;
    }
}
