<?php

declare(strict_types=1);

namespace App\Exceptions\BankAccount\Transfers\Pix;

use Illuminate\Http\Exceptions\HttpResponseException;

final class StatusPixException extends HttpResponseException
{
    private array $context;

    public function __construct(
        string $message,
        int $statusCode = 422,
        array $context = []
    ) {
        $this->context = $context;
        parent::__construct(
            response()->json(
                [
                    'message' => $message,
                    'context' => $context,
                ],
                $statusCode
            )
        );
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function getStatusCode(): int
    {
        return $this->getCode();
    }
}
